#!/usr/bin/env python3
"""
Test script for duplicate detection functionality in DeemixDownloader.py
This script tests the duplicate detection logic without actually downloading files.
"""

import os
import sys
import tempfile
import json
from pathlib import Path

# Add the current directory to the path so we can import DeemixDownloader
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the functions we want to test
from DeemixDownloader import (
    load_deemix_config, 
    predict_file_path, 
    build_existing_files_cache, 
    check_file_exists,
    sanitize_filename
)

def create_test_config():
    """Create a test configuration similar to the real one"""
    return {
        "downloadLocation": "/tmp/test_music",
        "tracknameTemplate": "%title%",
        "createArtistFolder": True,
        "artistNameTemplate": "%artist%",
        "createAlbumFolder": True,
        "albumNameTemplate": "%album%",
        "createSingleFolder": True,
        "illegalCharacterReplacer": "_"
    }

def create_test_track():
    """Create a test track info structure"""
    return {
        "title": "Test Song",
        "artist": {"name": "Test Artist"},
        "album": {"title": "Test Album"}
    }

def test_sanitize_filename():
    """Test filename sanitization"""
    print("Testing filename sanitization...")
    
    test_cases = [
        ("Normal Song", "Normal Song"),
        ("Song/With\\Slashes", "Song_With_Slashes"),
        ("Song:With*Illegal?Chars", "Song_With_Illegal_Chars"),
        ("Song<>With|Pipes", "Song__With_Pipes")
    ]
    
    for input_name, expected in test_cases:
        result = sanitize_filename(input_name)
        print(f"  '{input_name}' -> '{result}' (expected: '{expected}')")
        assert result == expected, f"Expected '{expected}', got '{result}'"
    
    print("✅ Filename sanitization tests passed!")

def test_predict_file_path():
    """Test file path prediction"""
    print("\nTesting file path prediction...")
    
    config = create_test_config()
    track = create_test_track()
    
    predicted_paths = predict_file_path(track, config)
    
    print(f"Predicted paths for '{track['title']}' by '{track['artist']['name']}':")
    for path in predicted_paths:
        print(f"  {path}")
    
    # Check that paths contain expected components
    expected_components = [
        config['downloadLocation'],
        track['artist']['name'],
        track['album']['title'],
        track['title']
    ]
    
    for path in predicted_paths:
        for component in expected_components:
            assert component in path, f"Expected '{component}' in path '{path}'"
    
    print("✅ File path prediction tests passed!")

def test_with_temp_files():
    """Test duplicate detection with actual temporary files"""
    print("\nTesting duplicate detection with temporary files...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test config pointing to temp directory
        config = create_test_config()
        config['downloadLocation'] = temp_dir
        
        # Create test directory structure
        artist_dir = os.path.join(temp_dir, "Test Artist")
        album_dir = os.path.join(artist_dir, "Test Album")
        os.makedirs(album_dir, exist_ok=True)
        
        # Create a test file
        test_file = os.path.join(album_dir, "Test Song.mp3")
        with open(test_file, 'w') as f:
            f.write("fake mp3 content")
        
        print(f"Created test file: {test_file}")
        
        # Build cache
        build_existing_files_cache(temp_dir)
        
        # Test duplicate detection
        track = create_test_track()
        exists, reason = check_file_exists(track, config)
        
        print(f"Duplicate check result: {exists} - {reason}")
        
        if exists:
            print("✅ Duplicate detection correctly identified existing file!")
        else:
            print("❌ Duplicate detection failed to identify existing file!")
            return False
        
        # Test with non-existing file
        track_new = {
            "title": "Non Existing Song",
            "artist": {"name": "Test Artist"},
            "album": {"title": "Test Album"}
        }
        
        exists_new, reason_new = check_file_exists(track_new, config)
        print(f"Non-existing file check: {exists_new} - {reason_new}")
        
        if not exists_new:
            print("✅ Duplicate detection correctly identified non-existing file!")
        else:
            print("❌ Duplicate detection incorrectly identified non-existing file as existing!")
            return False
    
    return True

def main():
    """Run all tests"""
    print("🧪 Testing Duplicate Detection Functionality")
    print("=" * 50)
    
    try:
        test_sanitize_filename()
        test_predict_file_path()
        
        if test_with_temp_files():
            print("\n🎉 All tests passed! Duplicate detection is working correctly.")
        else:
            print("\n❌ Some tests failed!")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
