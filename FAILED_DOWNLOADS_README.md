# Failed Downloads Tracking Feature

## Overview

The `DeemixDownloader.py` script now includes comprehensive failed downloads tracking functionality. This feature automatically records all songs that fail to download, along with detailed failure reasons and timestamps, saving this information to a text file for easy review and potential retry.

## Key Features

### 📝 **Automatic Failure Recording**
- Records every failed download attempt with detailed information
- Captures the original search query, failure reason, and timestamp
- No manual intervention required - works automatically during script execution

### 🔍 **Detailed Failure Reasons**
The system tracks various types of failures:
- **"No results found on <PERSON><PERSON>"** - Song not available in <PERSON><PERSON>'s catalog
- **"Download process failed"** - Deemix command-line tool errors
- **"Deezer API initialization failed"** - API connection issues
- **"Config loading failed"** - Configuration file problems
- **"Unexpected error"** - Network timeouts, permission issues, etc.

### 📄 **Readable Text File Output**
- Saves to `failed_downloads.txt` in the script's directory
- Human-readable format with clear structure
- Includes generation timestamp and total failure count
- One failure per section with query, reason, and time

### 🎯 **Smart File Handling**
- Creates file even when no failures occur (with success message)
- Overwrites previous reports (always shows latest run)
- Uses UTF-8 encoding for international character support
- Graceful error handling if file cannot be created

## How It Works

### 1. Failure Detection
The script monitors all points where `download_stats['failed']` is incremented:
- Deezer API initialization failures
- Configuration loading errors
- No search results found
- Subprocess (deemix) execution errors
- Unexpected exceptions during processing

### 2. Data Collection
For each failure, the system records:
```python
{
    'query': 'Original search query',
    'reason': 'Detailed failure reason',
    'timestamp': '2024-12-19 14:30:25'
}
```

### 3. File Generation
At the end of script execution, all failures are written to `failed_downloads.txt` with:
- Report header and generation timestamp
- Total failure count
- Numbered list of failures with details
- Clear separation between entries

## File Format

### With Failures
```
Failed Downloads Report
Generated: 2024-12-19 14:30:25
============================================================

Total failed downloads: 3

1. Query: Obscure Song Unknown Artist
   Reason: No results found on Deezer
   Time: 2024-12-19 14:28:15
----------------------------------------
2. Query: Copyrighted Song Major Label
   Reason: Download process failed: Command 'deemix' returned non-zero exit status 1
   Time: 2024-12-19 14:28:45
----------------------------------------
3. Query: Network Test Song
   Reason: Unexpected error: Network timeout
   Time: 2024-12-19 14:29:12
----------------------------------------
```

### No Failures
```
Failed Downloads Report
Generated: 2024-12-19 14:30:25
============================================================

🎉 No failed downloads! All songs were either downloaded successfully or skipped as duplicates.
```

## Usage

### Automatic Operation
The failed downloads tracking works automatically - no configuration needed:

```bash
python DeemixDownloader.py
```

After execution, check for `failed_downloads.txt` in the script directory.

### Sample Output During Execution
```
🎵 Deemix Downloader with Duplicate Detection
==================================================

[1] Searching: Bohemian Rhapsody Queen
Found: Bohemian Rhapsody by Queen
✅ Successfully downloaded: Bohemian Rhapsody by Queen

[2] Searching: Nonexistent Song Fake Artist
No results found for 'Nonexistent Song Fake Artist'

[3] Searching: Another Song Real Artist
Found: Another Song by Real Artist
❌ Failed to download 'Another Song Real Artist': Command 'deemix' returned non-zero exit status 1

⏱️  Total processing time: 45.67 seconds
============================================================
📊 DOWNLOAD STATISTICS
============================================================
Total songs processed: 3
✅ Successfully downloaded: 1
⏭️  Skipped (duplicates): 0
❌ Failed: 2

Duplicate detection saved 0.0% of downloads
Success rate: 33.3%
============================================================

📝 Failed downloads saved to: Z:\Scripts\Deezer\failed_downloads.txt
   Total failures: 2
```

## File Location

The `failed_downloads.txt` file is always created in the same directory as the `DeemixDownloader.py` script, regardless of:
- Where you run the script from
- What the music download directory is set to
- Current working directory

This ensures you can always find the failure report in a predictable location.

## Common Failure Reasons

### 1. **"No results found on Deezer"**
- **Cause**: Song not available in Deezer's catalog
- **Solution**: Try alternative search terms or check if song exists on Deezer manually
- **Example**: Very new releases, region-specific content, or misspelled artist/song names

### 2. **"Download process failed: Command 'deemix' returned non-zero exit status 1"**
- **Cause**: Deemix tool encountered an error during download
- **Solution**: Check deemix configuration, account status, or song availability
- **Example**: Premium account required, region restrictions, or corrupted files

### 3. **"Deezer API initialization failed"**
- **Cause**: Cannot connect to Deezer's API
- **Solution**: Check internet connection and Deezer service status
- **Example**: Network issues, Deezer maintenance, or API rate limiting

### 4. **"Unexpected error: [specific error]"**
- **Cause**: Various technical issues
- **Solution**: Check error details for specific troubleshooting
- **Example**: Network timeouts, permission issues, or disk space problems

## Troubleshooting

### File Not Created
If `failed_downloads.txt` is not created:
1. Check script directory permissions
2. Ensure sufficient disk space
3. Look for permission errors in console output

### Empty File
If file exists but appears empty:
1. Check file encoding (should be UTF-8)
2. Verify script completed execution
3. Check for console error messages

### Missing Failures
If known failures aren't recorded:
1. Ensure you're checking the correct directory
2. Verify script ran to completion
3. Check if failures occurred before tracking was initialized

## Integration with Existing Features

### Duplicate Detection Compatibility
- Failed downloads tracking works seamlessly with duplicate detection
- Skipped duplicates are NOT recorded as failures
- Only actual download attempts that fail are tracked

### Statistics Integration
- Failed count in statistics matches failed downloads file
- Provides cross-reference for verification
- Helps identify patterns in failure types

## Future Enhancements

Potential improvements for future versions:
- **Retry functionality**: Automatically retry failed downloads
- **Failure categorization**: Group failures by type for easier analysis
- **Export formats**: JSON, CSV, or XML output options
- **Historical tracking**: Append to existing files instead of overwriting
- **Notification system**: Email or desktop notifications for failures

## Testing

Run the included test suite to verify functionality:
```bash
python test_failed_downloads.py
```

The test suite validates:
- Failure recording accuracy
- File creation and format
- Content verification
- Edge cases (no failures, multiple failures)

## Benefits

### For Users
- **Quick identification** of problematic songs
- **Easy retry process** using the recorded queries
- **Pattern recognition** to identify systematic issues
- **Audit trail** for download sessions

### For Troubleshooting
- **Detailed error information** for support requests
- **Reproducible test cases** for debugging
- **Performance analysis** to identify common failure points
- **Quality assurance** for download success rates
