#!/usr/bin/env python3
"""
Spotify Authentication Setup for Docker Container

This script helps you authenticate with Spotify and create the necessary
cache file that can be used by the Docker container.
"""

import os
import spotipy
from spotipy.oauth2 import SpotifyOAuth

# Same config as DeemixDownloader.py
SPOTIPY_CLIENT_ID = '57f12a98a5aa492cb8ce0e29d6065555'
SPOTIPY_CLIENT_SECRET = '97b45bb40d3f4cd393e4c55abe3cba5f'
SPOTIPY_REDIRECT_URI = 'http://127.0.0.1:8888/callback'
scope = "user-library-read playlist-read-private playlist-read-collaborative"

def setup_spotify_auth():
    """Set up Spotify authentication and create cache file"""
    print("🎵 Spotify Authentication Setup")
    print("=" * 40)
    print("This will authenticate with Spotify and create a cache file")
    print("that can be used by the Docker container.\n")
    
    # Create cache directory if it doesn't exist
    cache_dir = os.path.join(os.path.dirname(__file__), 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    
    cache_path = os.path.join(cache_dir, '.spotify_cache')
    
    try:
        # Initialize Spotify with authentication
        auth_manager = SpotifyOAuth(
            client_id=SPOTIPY_CLIENT_ID,
            client_secret=SPOTIPY_CLIENT_SECRET,
            redirect_uri=SPOTIPY_REDIRECT_URI,
            scope=scope,
            cache_path=cache_path,
            open_browser=True  # This will open browser for authentication
        )
        
        sp = spotipy.Spotify(auth_manager=auth_manager)
        
        # Test the connection
        user = sp.current_user()
        print(f"✅ Successfully authenticated as: {user['display_name']}")
        print(f"📁 Cache file created at: {cache_path}")
        print("\n🐳 You can now run the Docker container!")
        print("   The container will use this authentication cache.")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you have internet connection")
        print("2. Check that the Spotify app credentials are correct")
        print("3. Ensure port 8888 is not blocked")
        return False

if __name__ == "__main__":
    success = setup_spotify_auth()
    if not success:
        exit(1)
    
    print("\n" + "=" * 40)
    print("🚀 Next steps:")
    print("1. Run: docker-compose up --build")
    print("2. Open: http://localhost:10000")
    print("3. Click 'Start Download' to begin!")
