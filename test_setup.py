#!/usr/bin/env python3
"""
Simple test script to validate the Docker setup files
"""
import os
import json
import sys

def test_files_exist():
    """Test that all required files exist"""
    required_files = [
        'DeemixDownloader.py',
        'app.py',
        'Dockerfile',
        'docker-compose.yml',
        'requirements.txt',
        'templates/index.html',
        'config/config.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing files:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print("✅ All required files exist")
        return True

def test_config_valid():
    """Test that config.json is valid"""
    try:
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        
        # Check if download location is set for Docker
        if config.get('downloadLocation') == '/app/downloads':
            print("✅ Config download location is set correctly for Dock<PERSON>")
            return True
        else:
            print(f"⚠️  Config download location: {config.get('downloadLocation')}")
            print("   Expected: /app/downloads")
            return False
    except Exception as e:
        print(f"❌ Config validation failed: {e}")
        return False

def test_directories_exist():
    """Test that required directories exist"""
    required_dirs = ['templates', 'config', 'downloads', 'cache']
    
    missing_dirs = []
    for dir in required_dirs:
        if not os.path.exists(dir):
            missing_dirs.append(dir)
    
    if missing_dirs:
        print("❌ Missing directories:")
        for dir in missing_dirs:
            print(f"  - {dir}")
        return False
    else:
        print("✅ All required directories exist")
        return True

def main():
    print("🧪 Testing Docker setup...")
    print("=" * 40)
    
    tests = [
        test_files_exist,
        test_config_valid,
        test_directories_exist
    ]
    
    results = []
    for test in tests:
        results.append(test())
        print()
    
    if all(results):
        print("🎉 All tests passed! Ready to build Docker container.")
        print("\nNext steps:")
        print("1. Run: docker-compose up --build")
        print("2. Open: http://localhost:10000")
        return 0
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
