#!/usr/bin/env python3
"""
Demo script showing the failed downloads tracking functionality
This creates a sample failed_downloads.txt file to demonstrate the feature
"""

import os
import sys
from datetime import datetime

# Add the current directory to the path so we can import DeemixDownloader
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the functions we want to demo
from DeemixDownloader import record_failed_download, save_failed_downloads, failed_downloads

def demo_failed_downloads_tracking():
    """Demonstrate the failed downloads tracking functionality"""
    print("🎵 Failed Downloads Tracking Demo")
    print("=" * 40)
    
    # Clear any existing failed downloads
    failed_downloads.clear()
    
    print("\n📝 Recording sample failed downloads...")
    
    # Simulate different types of failures
    sample_failures = [
        ("Bohemian Rhapsody Queen", "No results found on Deezer"),
        ("Stairway to Heaven Led Zeppelin", "Download process failed: Command 'deemix' returned non-zero exit status 1"),
        ("Hotel California Eagles", "Unexpected error: HTTPSConnectionPool(host='api.deezer.com', port=443): Read timed out"),
        ("Imagine John Lennon", "Download process failed: Track not available in your region"),
        ("Obscure Indie Song Unknown Artist", "No results found on <PERSON>zer")
    ]
    
    for query, reason in sample_failures:
        record_failed_download(query, reason)
        print(f"   ❌ {query} - {reason}")
    
    print(f"\n📊 Total failures recorded: {len(failed_downloads)}")
    
    print("\n💾 Saving failed downloads to file...")
    save_failed_downloads()
    
    # Show the file content
    script_dir = os.path.dirname(os.path.abspath(__file__))
    failed_file_path = os.path.join(script_dir, 'failed_downloads.txt')
    
    print(f"\n📄 Contents of {failed_file_path}:")
    print("-" * 60)
    
    try:
        with open(failed_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    except Exception as e:
        print(f"Error reading file: {e}")
    
    print("-" * 60)
    print("\n✅ Demo completed! The failed_downloads.txt file has been created.")
    print("   This file will be automatically generated whenever you run DeemixDownloader.py")
    print("   and encounter any download failures.")

if __name__ == "__main__":
    demo_failed_downloads_tracking()
