# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install deemix Python package
RUN pip install --no-cache-dir deemix

# Copy Python requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY app.py .
COPY DeemixDownloader.py .
COPY templates/ templates/
COPY config/ config/

# Create directories for downloads and cache
RUN mkdir -p /app/downloads /app/cache

# Create a non-root user for security
RUN useradd -m -u 1000 deemix && chown -R deemix:deemix /app

# Set PATH to include npm global binaries for the deemix user
ENV PATH="/usr/local/bin:$PATH"

USER deemix

# Expose port 10000
EXPOSE 10000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:10000/status || exit 1

# Run the Flask application
CMD ["python", "app.py"]
